"""Presentation layer for campaign recommendations."""
import os
from datetime import datetime
from jinja2 import Environment, FileSystemLoader
from utils.logger import get_logger
from pdf_helper import convert_html_to_pdf
logger = get_logger(__name__)

class ReportGenerator:
    def __init__(self, template_dir='.', output_dir='output_campaigns_test'):
        self.env = Environment(loader=FileSystemLoader(template_dir))
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

    async def generate_report(self, df_unlisted, uid):
        if df_unlisted.empty:
            logger.warning(f"No data available for report generation for user {uid}")
            return False

        # Get store information
        store_name = df_unlisted['store_name'].iloc[0]
        store_permalink = df_unlisted['store_permalink'].iloc[0]

        # Get tutorial URL based on country code
        country_code = store_permalink.split('/')[-2].split('.')[-1]
        tutorial_url = f'https://vendedores.mercadolibre.com.{country_code}/nota/como-crear-nuevas-campanas-en-product-ads'

        # Format numeric columns for display
        df_display = self._format_numeric_columns(df_unlisted)

        # Generate HTML
        try:
            template = self.env.get_template('productos_recomendados.html')
            html_output = template.render(
                df_unlisted=df_display,
                store_name=store_name,
                store_permalink=store_permalink,
                tutorial_url=tutorial_url
            )
        except Exception as e:
            logger.error(f"Error generating HTML for user {uid}: {str(e)}")
            return False

        # Generate PDF
        sanitized_store_name = store_name.replace(" ", "_").replace("/", "_")
        date_str = datetime.now().strftime("%Y%m%d")
        pdf_path = os.path.join(
            self.output_dir,
            f'produtos_recomendados_{sanitized_store_name}_{uid}_{date_str}.pdf'
        )

        success = await convert_html_to_pdf(html_output, pdf_path)
        if success:
            logger.info(f"Generated recommendation report for user {uid} at {pdf_path}")
        else:
            logger.error(f"Failed to generate PDF for user {uid}")

        return success

    def _format_numeric_columns(self, df):
        df = df.copy()
        if 'price' in df.columns:
            df['price'] = df['price'].astype(float)
        if 'sales_potential' in df.columns:
            df['sales_potential'] = df['sales_potential'].astype(float)
        if 'conversion' in df.columns:
            df['conversion'] = df['conversion'].astype(float)
        return df
