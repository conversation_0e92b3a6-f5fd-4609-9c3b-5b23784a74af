import os
import asyncio
import aiohttp
import logging
import pandas as pd
from input_data import get_go_bots_api_response
from datetime import datetime, timedelta
from aiohttp_client_cache import CachedSession, FileBackend

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler(f"logs/{__file__}.log"), logging.StreamHandler()],
)

logger = logging.getLogger()

DATE_FORMAT = "%Y-%m-%d"
DATE_TO = datetime.now().strftime(DATE_FORMAT)
DATE_FROM = (datetime.now() - timedelta(days=30)).strftime(DATE_FORMAT)

# Retry decorator for API calls
def retry_with_backoff(max_retries=5, initial_backoff=2, max_backoff=120):
    def decorator(func):
        async def wrapper(*args, **kwargs):
            retries = 0
            backoff = initial_backoff

            while retries <= max_retries:
                try:
                    return await func(*args, **kwargs)
                except aiohttp.ClientResponseError as e:
                    if e.status == 429:  # Too Many Requests
                        retries += 1
                        if retries > max_retries:
                            logger.error(f"Max retries reached for {func.__name__}")
                            raise

                        sleep_time = min(backoff * (2 ** (retries - 1)), max_backoff)
                        logger.warning(f"Rate limit hit, retrying in {sleep_time}s (retry {retries}/{max_retries})")
                        await asyncio.sleep(sleep_time)
                    else:
                        # For other errors, just log and continue
                        logger.error(f"API error in {func.__name__}: {e.status} - {e.message}")
                        return None
                except Exception as e:
                    logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
                    return None

            return None
        return wrapper
    return decorator

@retry_with_backoff(max_retries=5, initial_backoff=2, max_backoff=120)
async def obter_advertiser_brand_ads(session, headers):
    """
    Get Brand Ads advertiser information
    """
    url = "https://api.mercadolibre.com/advertising/advertisers?product_id=BADS"
    headers_local = dict(headers)
    headers_local["Content-Type"] = "application/json"
    headers_local["Api-Version"] = "1"

    try:
        async with session.get(url, headers=headers_local) as response:
            response.raise_for_status()
            data = await response.json()
            advertiser_data = data["advertisers"][0] if data and data.get("advertisers") else None
            if advertiser_data:
                return advertiser_data
            return {}
    except aiohttp.ClientResponseError as e:
        if e.status == 429:
            # Let the retry decorator handle this
            logger.warning("Rate limit hit (429) when fetching Brand Ads advertiser ID")
            raise
        else:
            logger.error(f"API error: {e.status} - {e.message} when fetching Brand Ads advertiser ID")
            return {}
    except Exception as e:
        logger.error(f"Unexpected error fetching Brand Ads advertiser ID: {str(e)}")
        return {}

@retry_with_backoff(max_retries=5, initial_backoff=2, max_backoff=120)
async def listar_campanhas_brand_ads(session, headers, advertiser_id, date_from, date_to, limit=50):
    """
    List Brand Ads campaigns
    """
    campaigns = []

    headers_local = dict(headers)
    headers_local["Api-Version"] = "1"

    offset = 0
    while True:
        url = (f"https://api.mercadolibre.com/advertising/advertisers/{advertiser_id}/brand_ads/campaigns"
            f"?date_from={date_from}"
            f"&date_to={date_to}")

        try:
            async with session.get(url, headers=headers_local) as resp:
                resp.raise_for_status()

                data = await resp.json() or {}
                results = data.get("campaigns", [])
                if not results:
                    break

                campaigns.extend(results)

                paging = data.get("paging", {})
                total = paging.get("total", 0)

                offset_next = offset + limit
                if offset_next >= total:
                    break
                offset = offset_next

                # Add a small delay between requests to avoid hitting rate limits
                await asyncio.sleep(0.5)

        except aiohttp.ClientResponseError as e:
            if e.status == 429:
                # Let the retry decorator handle this
                logger.warning(f"Rate limit hit (429) when listing Brand Ads campaigns for advertiser {advertiser_id}")
                raise
            else:
                logger.error(f"API error: {e.status} - {e.message} for URL: {url}")
                return None
        except Exception as e:
            logger.error(f"Unexpected error listing Brand Ads campaigns: {str(e)}")
            return None

    logger.info(f"[INFO] Found {len(campaigns)} Brand Ads campaigns for advertiser {advertiser_id}")
    return campaigns

@retry_with_backoff(max_retries=5, initial_backoff=2, max_backoff=120)
async def obter_metricas_campanha(session, headers, advertiser_id, campaign_id, date_from, date_to):
    """
    Get metrics for a specific Brand Ads campaign
    """
    url = f"https://api.mercadolibre.com/advertising/advertisers/{advertiser_id}/brand_ads/campaigns/{campaign_id}/metrics?date_from={date_from}&date_to={date_to}"

    headers_local = dict(headers)

    try:
        async with session.get(url, headers=headers_local) as response:
            response.raise_for_status()
            return await response.json()
    except aiohttp.ClientResponseError as e:
        if e.status == 429:
            # Let the retry decorator handle this
            logger.warning(f"Rate limit hit (429) when fetching Brand Ads campaign metrics for campaign {campaign_id}")
            raise
        else:
            logger.error(f"API error: {e.status} - {e.message} when fetching Brand Ads campaign metrics")
            return None
    except Exception as e:
        logger.error(f"Unexpected error fetching Brand Ads campaign metrics: {str(e)}")
        return None

async def get_brand_ads_data_by_seller(session, seller_data):
    """
    Process Brand Ads data for a specific seller
    """
    merchant_info = seller_data.get('merchant', {})
    merchant_name = merchant_info.get('name', 'Unknown') if merchant_info else 'Unknown'
    merchant_id = merchant_info.get('id', 'Unknown') if merchant_info else 'Unknown'

    access_token = seller_data.get('access_token')
    if not access_token:
        logger.error(f"No access token for merchant {merchant_name} (ID: {merchant_id})")
        return {
            "merchant": merchant_info,
            "status": "error",
            "error_message": "No access token available",
            "has_brand_ads": False
        }

    headers = {'Authorization': f'Bearer {access_token}'}

    # Get Brand Ads advertiser information
    bads_info = await obter_advertiser_brand_ads(session, headers)
    advertiser_id = bads_info.get("advertiser_id")

    if not advertiser_id:
        logger.error(f"[ERRO] Não foi possível obter advertiser de Brand Ads para {merchant_name} (ID: {merchant_id})")
        return {
            "merchant": merchant_info,
            "status": "error",
            "error_message": "Brand Ads advertiser ID not found",
            "has_brand_ads": False
        }

    # List Brand Ads campaigns
    campaigns = await listar_campanhas_brand_ads(session, headers, advertiser_id, DATE_FROM, DATE_TO)

    if not campaigns:
        logger.warning(f"[AVISO] Nenhuma campanha de Brand Ads encontrada para {merchant_name} (ID: {merchant_id})")
        return {
            "merchant": merchant_info,
            "advertiser_id": advertiser_id,
            "country": bads_info.get("site_id"),
            "status": "success",
            "has_brand_ads": False,
            "campaigns_count": 0
        }

    # Add additional information to campaigns
    for campaign in campaigns:
        campaign["country"] = bads_info["site_id"]
        campaign["merchant"] = merchant_info

        # Get campaign metrics
        metrics = await obter_metricas_campanha(session, headers, advertiser_id, campaign["campaign_id"], DATE_FROM, DATE_TO)
        if metrics and "summary" in metrics:
            campaign["metrics"] = metrics["summary"]

    logger.info(f"[INFO] Found {len(campaigns)} Brand Ads campaigns for {merchant_name} (ID: {merchant_id})")
    return campaigns

async def main():
    os.makedirs('output_brand_ads', exist_ok=True)

    # Create a cached session with FileBackend
    cache = FileBackend(
        cache_name='brand_ads_cache',
        expire_after=3600,  # Cache expires after 1 hour
    )

    async with CachedSession(cache=cache) as session:
        logger.info("Using cached session with FileBackend (expires=3600s)")

        go_bots_data = await get_go_bots_api_response(session)
        if not go_bots_data:
            logger.error("Failed to fetch GoBots data")
            return

        tasks = [get_brand_ads_data_by_seller(session, item) for item in go_bots_data]
        results = await asyncio.gather(*tasks)

        # Process results
        campaign_results = []
        merchant_summary = []

        for result in results:
            if not result:
                continue

            # Check if this is a campaign list or a merchant summary
            if isinstance(result, list):
                # It's a list of campaigns
                campaign_results.extend(result)

                # Extract merchant info from the first campaign for summary
                if result:
                    first_campaign = result[0]
                    merchant_summary.append({
                        "merchant": first_campaign.get("merchant"),
                        "country": first_campaign.get("country"),
                        "advertiser_id": first_campaign.get("advertiser_id"),
                        "status": "success",
                        "has_brand_ads": True,
                        "campaigns_count": len(result)
                    })
            else:
                # It's a merchant summary
                merchant_summary.append(result)

        # Save merchant summary
        if merchant_summary:
            summary_df = pd.json_normalize(merchant_summary)
            summary_df.to_csv('output_brand_ads/brand_ads_merchant_summary.csv', index=False, encoding='utf-8')
            logger.info(f"Saved Brand Ads summary for {len(merchant_summary)} merchants")

            # Count merchants with Brand Ads
            merchants_with_brand_ads = sum(1 for item in merchant_summary if item.get("has_brand_ads", False))
            logger.info(f"Found {merchants_with_brand_ads} merchants with Brand Ads out of {len(merchant_summary)} total")

        # Process campaign results if any
        if campaign_results:
            # Convert to DataFrame
            results_df = pd.json_normalize(campaign_results)

            # Save raw results
            results_df.to_csv('output_brand_ads/brand_ads_results.csv', index=False, encoding='utf-8')

            # Group by merchant.id and calculate metrics, keeping merchant.name
            try:
                grouped_df = results_df.groupby(['merchant.id', 'merchant.name', "country"]).agg(
                    total_cost=('metrics.consumed_budget', 'sum'),
                    total_impressions=('metrics.prints', 'sum'),
                    total_clicks=('metrics.clicks', 'sum'),
                    total_campaigns=('campaign_id', 'count')
                ).reset_index()

                # Round numeric values to 2 decimal places
                for col in ['total_cost']:
                    if col in grouped_df.columns:
                        grouped_df[col] = grouped_df[col].round(2)

                # Save grouped results
                grouped_df.to_csv('output_brand_ads/brand_ads_grouped_results.csv', index=False, encoding='utf-8')
                logger.info("Brand Ads campaign data processing completed successfully")

            except Exception as e:
                logger.error(f"Error grouping Brand Ads campaign data: {str(e)}")
        else:
            logger.warning("No Brand Ads campaigns found for any merchant")

if __name__ == "__main__":
    asyncio.run(main())
