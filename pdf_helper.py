import os
import asyncio
import subprocess
from playwright.async_api import async_playwright
import logging

logger = logging.getLogger(__name__)

async def convert_html_to_pdf(html_content: str, pdf_output_path: str) -> bool:
    try:
        async with async_playwright() as p:
            browser = await p.chromium.launch()
            page = await browser.new_page()
            
            await page.set_content(html_content, timeout=80_000)
            
            pdf_bytes = await page.pdf(
                width="14.8in",
                height="21in",
                margin={"top": "0", "right": "0", "bottom": "0", "left": "0"},
                print_background=True,
                scale=1
            )
            
            await browser.close()

            ghostscript_cmd = [
                'gswin64c',
                '-sDEVICE=pdfwrite',
                '-dPDFSETTINGS=/ebook',
                '-dNOPAUSE',
                '-dQUIET',
                '-dBATCH',
                '-sOutputFile=-',
                '-'
            ]
            
            proc = await asyncio.to_thread(
                subprocess.run,
                ghostscript_cmd,
                input=pdf_bytes,
                capture_output=True,
                check=True
            )
            
            with open(pdf_output_path, 'wb') as f:
                f.write(proc.stdout)
            
            logger.info(f"Successfull PDF conversion: {pdf_output_path}")
            return True

    except Exception as e:
        if os.path.exists(pdf_output_path):
            os.remove(pdf_output_path)
        logger.info(f"Error during PDF conversion: {str(e)}, file: {pdf_output_path}")
        return False
