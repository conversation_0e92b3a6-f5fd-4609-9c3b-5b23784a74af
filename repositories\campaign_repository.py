"""Repository layer for campaign recommendations."""
import asyncio
from datetime import datetime
from aiohttp_client_cache import CachedSession
from utils.logger import get_logger
from campaign_whatsapp_report import (
    obter_advertiser_id_pads,
    listar_campanhas_advertiser,
    listar_product_ads_items,
    fetch_campaign_data
)
from input_data import build_output
from services.gobots_service import GoBotsService

logger = get_logger(__name__)

class CampaignRepository:
    def __init__(self, session: CachedSession):
        self.session = session
        self.gobots_service = GoBotsService()

    async def get_access_token(self, user_id: int):
        return await self.gobots_service.get_user_access_token(user_id)

    async def get_advertiser_id(self, headers):
        return await obter_advertiser_id_pads(self.session, headers)

    async def get_campaigns(self, headers, advertiser_id, date_from, date_to):
        return await listar_campanhas_advertiser(
            self.session, headers, advertiser_id, date_from, date_to
        )

    async def get_campaign_products(self, access_token, advertiser_id, campaigns, date_from, date_to):
        campaign_products = []
        for campaign in campaigns:
            if campaign['date_created'].split("-")[0] == '2025':
                campaign['date_created'] = date_from
            campaign_data = await fetch_campaign_data(
                self.session,
                campaign['id'],
                access_token,
                campaign['date_created'].split("T")[0],
                date_to
            )
            if campaign_data and campaign_data.get('metrics') and campaign_data.get('metrics').get("clicks"):
                this_campaign_products = await listar_product_ads_items(
                    self.session,
                    access_token,
                    advertiser_id,
                    campaign['id'],
                    campaign['date_created'].split("T")[0],
                    date_to
                )
                campaign_products.extend(this_campaign_products)
        return campaign_products

    async def get_product_data(self, uid, access_token, days=30):
        return await build_output(self.session, uid, access_token, days)
