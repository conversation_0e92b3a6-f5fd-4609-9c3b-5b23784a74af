import os

__access_token = os.environ.get('GOBOTS_TOKEN')


def extract_user_id_from_token(access_token: str) -> str:
    return access_token.split("-")[-1]

def get_access_token_from_gobots_data(user_id, data):
    for item in data:
        if item.get('user_id') == user_id:
            return item.get('access_token')
        return None
    
async def get_api_response(session):
    url = 'https://askhere.gobots.com.br/ml/all'
    headers = {'Authorization': f'Bearer {__access_token}'}
    
    async with session.get(url, headers=headers) as response:
        if response.status == 200:
            return await response.json()
        return None
