import os
import asyncio
from aiohttp_client_cache import CachedSession
import logging
from aiohttp_client_cache import FileBackend
import pandas as pd
from input_data import get_go_bots_api_response
from campaign_whatsapp_report import listar_product_ads_items, obter_advertiser_pads, listar_campanhas_advertiser
from datetime import datetime, timedelta

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler(f"logs/{__file__}.log"), logging.StreamHandler()],
)

logger = logging.getLogger()

DATE_FORMAT = "%Y-%m-%d"
DATE_TO = datetime.now().strftime(DATE_FORMAT)
DATE_FROM = (datetime.now() - timedelta(days=30)).strftime(DATE_FORMAT)


async def check_if_product_was_listed(session, seller_data, suggested_products):
    access_token = seller_data.get('access_token')
    if not access_token:
        logger.error("No access token for user!!!!!")
        return

    headers = {'Authorization': f'Bearer {access_token}'}

    pads_info = await obter_advertiser_pads(session, headers)
    advertiser_id = pads_info.get("advertiser_id")
    if not advertiser_id:
        logger.error("[ERRO] Não foi possível obter advertiser de PADS !!!!!")
        return

    # 1) Listar campanhas
    campaigns = await listar_campanhas_advertiser(session, headers, advertiser_id, DATE_FROM, DATE_TO)

    if not campaigns:
        logger.error("[ERRO] Nenhuma campanha encontrada !!!!")
        return 
    
    campaign_products = []

    for campaign in campaigns:
        if campaign['date_created'].split("-")[0] != '2025':
            campaign['date_created'] = DATE_FROM
        this_campaign_products = await listar_product_ads_items(session, access_token, advertiser_id, campaign['id'],  campaign['date_created'].split("T")[0], DATE_TO)
        campaign_products.extend(this_campaign_products)
    
    campaign_products_ids_set = set(product['item_id'] for product in campaign_products)

    seller_suggested_products = suggested_products[suggested_products['seller_id'] == seller_data['user_id']]
    
    for tuple in seller_suggested_products.itertuples():
        if tuple.product_id in campaign_products_ids_set:
            suggested_products.at[tuple.Index, 'listed'] = True
        else:
            suggested_products.at[tuple.Index, 'listed'] = False

    suggested_products.to_csv('products_suggestions_sent.csv', index=False, encoding='utf-8')


async def main():
    os.makedirs('output_campaigns_test', exist_ok=True)
    
    # Set up cache directory
    cache_dir = os.path.join(os.getcwd(), 'cache')
    os.makedirs(cache_dir, exist_ok=True)

    # Configure the cache with a 1-hour expiration time (3600 seconds)
    logger.info(f"Setting up cache in directory: {cache_dir} with 1-hour expiration")

    # Create a file cache with 1-hour expiration
    cache = FileBackend(
        cache_name=cache_dir,
        expire_after=3600*24,
        allowed_methods=('GET', 'POST'),  # Cache both GET and POST requests
        include_headers=True,             # Include headers in the cache key
        ignored_params=['access_token'],  # Don't include access tokens in cache keys for security
        cache_control=True                # Respect Cache-Control headers from the server
    )


    # Create a cached session with additional options
    async with CachedSession(
        cache=cache,
        retry_429=True,                   # Automatically retry on rate limit errors
        retry_backoff_factor=0.5,         # Start with a 0.5s delay, then 1s, 2s, etc.
        retry_max_retries=5               # Maximum number of retries
    ) as session:
        logger.info("Using cached session with FileBackend (expires=3600s)")


        go_bots_data = await get_go_bots_api_response(session)
        if not go_bots_data:
            logger.error("Failed to fetch GoBots data")
            return
        
        suggested_products = pd.read_csv('products_suggestions_sent.csv')
        sellers_ids_to_check = set(suggested_products['seller_id'].where(suggested_products['listed'].isna()).dropna().astype(int))
        sellers = [item for item in go_bots_data if item['user_id'] in sellers_ids_to_check]
        tasks = [check_if_product_was_listed(session, item, suggested_products) for item in sellers]
        await asyncio.gather(*tasks)
    

if __name__ == "__main__":
    asyncio.run(main())
