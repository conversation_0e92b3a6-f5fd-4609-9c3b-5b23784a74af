import os
import asyncio
import aiohttp
import logging
import pandas as pd
from input_data import get_go_bots_api_response
from campaign_whatsapp_report import obter_advertiser_pads, listar_campanhas_advertiser
from datetime import datetime, timedelta

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler(f"logs/{__file__}.log"), logging.StreamHandler()],
)

logger = logging.getLogger()

DATE_FORMAT = "%Y-%m-%d"
DATE_TO = datetime.now().strftime(DATE_FORMAT)
DATE_FROM = (datetime.now() - timedelta(days=30)).strftime(DATE_FORMAT)


async def get_ads_data_by_seller(session, seller_data):
    access_token = seller_data.get('access_token')
    if not access_token:
        logger.error("No access token for user!!!!!")
        return

    headers = {'Authorization': f'Bearer {access_token}'}

    pads_info = await obter_advertiser_pads(session, headers)
    advertiser_id = pads_info.get("advertiser_id")
    if not advertiser_id:
        logger.error("[ERRO] Não foi possível obter advertiser de PADS !!!!!")
        return

    # 1) Listar campanhas
    campaigns = await listar_campanhas_advertiser(session, headers, advertiser_id, DATE_FROM, DATE_TO)

    if not campaigns:
        logger.error("[ERRO] Nenhuma campanha encontrada !!!!")
        return

    for campaign in campaigns:
        campaign["country"] = pads_info["site_id"]
        campaign["merchant"] = seller_data.get("merchant")
    
    return campaigns

async def main():
    os.makedirs('output_campaigns_test', exist_ok=True)

    async with aiohttp.ClientSession() as session:

        go_bots_data = await get_go_bots_api_response(session)
        if not go_bots_data:
            logger.error("Failed to fetch GoBots data")
            return

        tasks = [get_ads_data_by_seller(session, item) for item in go_bots_data]
        results = await asyncio.gather(*tasks)

        normalized_dfs = [pd.json_normalize(result) for result in results if result]
        results_df = pd.concat(normalized_dfs)
        
        results_df.to_csv('output_campaigns_test/results.csv', index=False, encoding='utf-8')

        results_df = pd.read_csv('output_campaigns_test/results.csv', encoding='utf-8')

        # Group by merchant.id and calculate metrics, keeping merchant.name
        grouped_df = results_df.groupby(['merchant.id', 'merchant.name', "country"]).agg(
            total_cost=('metrics.cost', 'sum'),
            total_campaigns=('id', 'count')
        ).reset_index()

        # Round total_cost to 2 decimal places
        grouped_df['total_cost'] = grouped_df['total_cost'].round(2)

        # Save grouped results to a CSV file
        grouped_df.to_csv('output_campaigns_test/grouped_results.csv', index=False, encoding='utf-8')


if __name__ == "__main__":
    asyncio.run(main())
