<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Produtos Recomendados para Campanhas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .header-logo {
            max-height: 80px;
            margin: 20px 0;
        }
        .product-image {
            max-width: 100px;
            max-height: 100px;
            object-fit: contain;
        }
        .recommendation-badge {
            background-color: #10B981;
            color: white;
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            display: inline-block;
            margin-bottom: 0.5rem;
        }
        .info-box {
            background-color: #f8f9fa;
            border-left: 4px solid #4F46E5;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="text-center">
            <img src="https://gobots.ai/wp-content/uploads/2022/02/logo-gobots-solucao-platinum-mercado-livre-inteligencia-artificial-cor.png" alt="GoBots Logo" class="header-logo img-fluid">
            <h1 class="mt-4 mb-4">Produtos Recomendados para Campanhas de Anúncios</h1>
        </div>

        <div class="info-box">
            <h5>Loja: <a href="{{store_permalink}}" target="_blank">{{store_name}}</a></h5>
            <p>Os produtos abaixo foram identificados como bons candidatos para campanhas de Product Ads, mas ainda não estão sendo anunciados.</p>
            <p>Adicionar estes produtos às suas campanhas pode aumentar suas vendas e melhorar o desempenho geral da sua loja.</p>
        </div>

        {% if df_unlisted.shape[0] == 1 and 'product_group' not in df_unlisted.columns %}
        <!-- Case 1: Empty DataFrame with just store info -->
        <div class="alert alert-info mt-5">
            <h4 class="alert-heading">Nenhum produto recomendado encontrado!</h4>
            <p>Não encontramos produtos que ainda não estejam em campanhas e que sejam recomendados para anúncios.</p>
            <p>Isso pode significar que você já está anunciando todos os produtos recomendados, o que é uma ótima estratégia!</p>
        </div>
        {% else %}
        <!-- Case 2: DataFrame with products -->
        {% set recommended_products = df_unlisted %}

        {% if recommended_products.empty %}
        <!-- Case 2.1: DataFrame exists but is empty -->
        <div class="alert alert-info mt-5">
            <h4 class="alert-heading">Nenhum produto recomendado encontrado!</h4>
            <p>Não encontramos produtos que ainda não estejam em campanhas e que sejam recomendados para anúncios.</p>
            <p>Isso pode significar que você já está anunciando todos os produtos recomendados, o que é uma ótima estratégia!</p>
        </div>
        {% else %}
        <!-- Case 2.2: DataFrame has products -->
        <h2 class="mt-5">Produtos Prioritários para Anúncios</h2>
        <p>Estes produtos têm alto potencial de vendas e devem ser considerados prioritários para suas campanhas.</p>

        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th class="text-center">Produto</th>
                        <th class="text-center">Título</th>
                        <th class="text-center">Preço</th>
                        <th class="text-center">Visitas</th>
                        <th class="text-center">Vendas</th>
                        <th class="text-center">Conversão</th>
                        <th class="text-center">Potencial de Vendas</th>
                        <th class="text-center">Classe ABC</th>
                    </tr>
                </thead>
                <tbody>
                    {% for _, row in recommended_products.iterrows() %}
                    <tr>
                        <td class="text-center align-middle">
                            <span class="recommendation-badge">Recomendado</span><br>
                            <a href="{{ row['permalink'] }}" target="_blank">
                                <img src="{{ row['image_url'] }}" alt="Imagem do Produto" class="product-image">
                            </a>
                        </td>
                        <td class="align-middle">{{ row['title'] }}</td>
                        <td class="text-center align-middle">R$ {{ "%.2f"|format(row['price']) }}</td>
                        <td class="text-center align-middle">{{ row['visits'] }}</td>
                        <td class="text-center align-middle">{{ row['sales'] }}</td>
                        <td class="text-center align-middle">{{ "%.1f"|format(row['conversion']*100) }}%</td>
                        <td class="text-center align-middle">R$ {{ "%.2f"|format(row['sales_potential']) }}</td>
                        <td class="text-center align-middle">{{ row['abc_class'] }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}
        {% endif %}

        <div class="info-box mt-4 mb-5">
            <h5>Recomendações para Campanhas de Product Ads</h5>
            <p>O Mercado Livre possui um <a href="https://vendedores.mercadolivre.com.br/nota/como-criar-campanhas-publicitarias-no-product-ads" target="_blank">tutorial explicativo</a> sobre como criar campanhas de Product Ads.</p>
            <p>Nossa sugestão para o ACOS (Advertising Cost of Sale) é entre 3% e 8%, dependendo da margem do produto.</p>
            <p>Para melhores resultados, comece com produtos que têm alta taxa de conversão e bom potencial de vendas.</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>