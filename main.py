"""Main entry point for the campaign recommendation system."""
import os
import asyncio
from datetime import datetime, timedelta
from aiohttp_client_cache import CachedSession
from aiohttp_client_cache.backends import FileBackend
from utils.logger import get_logger
from services.campaign_service import CampaignService
from presentation.report_generator import ReportGenerator
from services.gobots_service import GoBotsService
from repositories.campaign_repository import CampaignRepository


logger = get_logger(__name__)

DATE_FORMAT = "%Y-%m-%d"
DATE_TO = datetime.now().strftime(DATE_FORMAT)
DATE_FROM = (datetime.now() - timedelta(days=30)).strftime(DATE_FORMAT)

async def main():
    # Set up cache
    cache_dir = os.path.join(os.getcwd(), 'cache')
    os.makedirs(cache_dir, exist_ok=True)
    
    cache = FileBackend(
        cache_name=cache_dir,
        expire_after=3600,
        allowed_methods=('GET'),
        include_headers=True,
        ignored_params=['access_token'],
        cache_control=True
    )

    async with CachedSession(cache=cache) as session:
        # Initialize components
        gobots_service = GoBotsService()
        repository = CampaignRepository(session)
        service = CampaignService(repository)
        report_generator = ReportGenerator()

        with open('merchants.txt') as f:
            merchant_ids = [uid.strip() for uid in f.read().split(',')]
        
        sellers_data = await gobots_service.get_users_by_merchants(merchant_ids)
        
        # Process each user
        for seller in sellers_data:            
            # Process recommendations
            uid = seller['access_token']

            df_unlisted, error = await service.process_recommendations(
                str(uid), sellers_data, DATE_FROM, DATE_TO
            )
            
            if error:
                logger.warning(f"Error processing recommendations for user {uid}: {error}")
                continue

            # Generate report
            if df_unlisted is not None:
                await report_generator.generate_report(df_unlisted, uid)

if __name__ == "__main__":
    asyncio.run(main())
